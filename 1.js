// ==UserScript==
// @name         PDF拦截器与去水印脚本
// @namespace    http://tampermonkey.net/
// @version      2.2
// @description  拦截PDF请求，去除水印，启用打印，智能下载
// <AUTHOR>
// @match        *://newview3.gtcloud.cn/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // ============== 去水印和打印功能 ==============
    // 1. 立即禁用水印生成函数
    const originalWatermark = window.watermark;
    window.watermark = function() { /* 空实现 */ };

    // 2. 修复打印权限
    if (typeof authMap !== 'undefined') {
        authMap.print = "1"; // 启用打印
    }

    // 3. 覆盖打印服务的水印逻辑
    if (typeof PDFPrintService !== 'undefined') {
        const originalUseRenderedPage = PDFPrintService.prototype.useRenderedPage;
        PDFPrintService.prototype.useRenderedPage = function(printItem) {
            this.throwIfInactive();
            const img = document.createElement("img");
            img.style.width = printItem.width;
            img.style.height = printItem.height;
            const scratchCanvas = this.scratchCanvas;

            // 原始图片生成逻辑
            if ("toBlob" in scratchCanvas && !this.disableCreateObjectURL) {
                scratchCanvas.toBlob(blob => {
                    img.src = URL.createObjectURL(blob);
                });
            } else {
                img.src = scratchCanvas.toDataURL();
            }

            const wrapper = document.createElement("div");
            wrapper.appendChild(img);
            // 移除了添加水印的代码
            this.printContainer.appendChild(wrapper);
            return new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = reject;
            });
        };
    }

    // 4. 清理已存在的水印元素
    const removeExistingWatermarks = () => {
        document.querySelectorAll('.mask_div').forEach(el => el.remove());
    };

    // 5. 监听DOM变化持续清理
    const observer = new MutationObserver(removeExistingWatermarks);
    observer.observe(document.body, { childList: true, subtree: true });

    // 6. 初始化后立即执行一次清理
    setTimeout(removeExistingWatermarks, 1000);

    // ============== 辅助函数 ==============
    // 监控打印事件，确保打印样式正常
    const fixPrintStyle = () => {
        const printStyle = document.querySelector('style[media="print"]');
        if (printStyle && printStyle.textContent.includes('display: none')) {
            printStyle.remove();
        }
    };

    // 初始化时修复打印样式
    fixPrintStyle();

    // 监听动态修改的打印样式
    new MutationObserver(fixPrintStyle).observe(document.head, {
        childList: true,
        subtree: true
    });

    // ============== PDF拦截功能 ==============

    // 保存原始的fetch函数
    const originalFetch = window.fetch;

    // 存储已处理的PDF文件名，防止重复
    const processedFiles = new Set();

    // 当前拦截的PDF信息
    let currentPdf = null;

    // 重写fetch函数
    window.fetch = function(...args) {
        return originalFetch.apply(this, args).then(response => {
            // 检查响应的Content-Type是否为PDF
            const contentType = response.headers.get('content-type');

            if (contentType && contentType.includes('application/pdf')) {
                console.log('检测到PDF响应:', response.url);

                // 克隆响应以避免消费原始响应
                const clonedResponse = response.clone();

                // 处理PDF数据
                handlePdfResponse(clonedResponse, response.url);
            }

            return response;
        });
    };

    // 处理PDF响应的函数
    async function handlePdfResponse(response, url) {
        try {
            // 获取响应的ArrayBuffer
            const arrayBuffer = await response.arrayBuffer();

            // 检查PDF是否有实际内容（大小检查）
            if (arrayBuffer.byteLength < 1024) { // 小于1KB的可能是空文件
                console.log('PDF文件过小，跳过:', url);
                return;
            }

            // 验证PDF格式
            const uint8Array = new Uint8Array(arrayBuffer);
            const header = new TextDecoder().decode(uint8Array.slice(0, 5));
            if (!header.startsWith('%PDF')) {
                console.log('不是有效的PDF文件，跳过:', url);
                return;
            }

            // 等待DOM加载完成后再提取标题
            await waitForElement();

            // 提取文档标题
            const docTitle = extractDocumentTitle();

            // 生成文件名
            let filename = docTitle || `document_${Date.now()}`;

            // 清理文件名
            filename = sanitizeFilename(filename);

            // 检查是否重名
            if (processedFiles.has(filename)) {
                console.log('文件名重复，跳过:', filename);
                return;
            }

            // 转换为base64
            const base64String = arrayBufferToBase64(arrayBuffer);

            // 记录已处理的文件
            processedFiles.add(filename);

            // 保存当前PDF信息
            currentPdf = {
                base64: base64String,
                filename: filename + '.pdf',
                title: docTitle,
                url: url
            };

            console.log('PDF已拦截:', filename);

            // 显示控制面板和通知
            showControlPanel();
            showNotification(`已拦截PDF: ${filename}`);

        } catch (error) {
            console.error('处理PDF响应时出错:', error);
        }
    }

    // 等待DOM元素加载
    function waitForElement() {
        return new Promise((resolve) => {
            const checkElement = () => {
                const xpath = '//*[@id="app"]/div/div[2]/div[2]/div/div[2]/div/div[1]/div[4]/div/div[1]/div[3]/div/div/span[1]';
                const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);

                if (result.singleNodeValue) {
                    resolve();
                } else if (document.readyState === 'complete') {
                    // 如果页面已完全加载但找不到元素，也继续执行
                    setTimeout(resolve, 500);
                } else {
                    setTimeout(checkElement, 200);
                }
            };
            checkElement();
        });
    }

    // 从XPath提取文档标题
    function extractDocumentTitle() {
        try {
            // 使用指定的XPath路径
            const xpath = '//*[@id="app"]/div/div[2]/div[2]/div/div[2]/div/div[1]/div[4]/div/div[1]/div[3]/div/div/span[1]';
            const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);

            if (result.singleNodeValue) {
                const title = result.singleNodeValue.textContent.trim();
                console.log('从XPath提取到标题:', title);
                return title;
            }

            // 备用方案1：尝试其他可能的选择器
            const titleSelectors = [
                'h1', 'h2', 'h3',
                '.title', '.document-title', '.file-name',
                '[class*="title"]', '[class*="name"]'
            ];

            for (const selector of titleSelectors) {
                const element = document.querySelector(selector);
                if (element && element.textContent.trim()) {
                    const title = element.textContent.trim();
                    console.log(`从选择器 ${selector} 提取到标题:`, title);
                    return title;
                }
            }

            // 备用方案2：从页面标题提取
            if (document.title && document.title !== 'Document') {
                console.log('从页面标题提取:', document.title);
                return document.title.trim();
            }

            console.log('未找到文档标题，使用默认命名');
            return null;

        } catch (error) {
            console.warn('提取文档标题失败:', error);
            return null;
        }
    }

    // 清理文件名
    function sanitizeFilename(filename) {
        if (!filename) {
            return `doc_${Date.now()}`;
        }

        // 移除特殊字符，保留中文、英文、数字、下划线、连字符、括号
        let cleaned = filename
            .replace(/[<>:"/\\|?*]/g, '') // 移除不允许的字符
            .replace(/\s+/g, '_') // 空格替换为下划线
            .replace(/[^\u4e00-\u9fa5\w\-_()（）]/g, '') // 保留中文、字母、数字、下划线、连字符、括号
            .replace(/_+/g, '_') // 合并多个下划线
            .replace(/^_|_$/g, ''); // 移除开头和结尾的下划线

        // 如果清理后为空，使用默认名称
        if (!cleaned) {
            cleaned = `doc_${Date.now()}`;
        }

        // 限制长度
        if (cleaned.length > 100) {
            cleaned = cleaned.substring(0, 100);
        }

        return cleaned;
    }

    // 将ArrayBuffer转换为Base64的函数
    function arrayBufferToBase64(buffer) {
        let binary = '';
        const bytes = new Uint8Array(buffer);
        const len = bytes.byteLength;

        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(bytes[i]);
        }

        return btoa(binary);
    }

    // 从base64下载PDF的函数
    function downloadPdfFromBase64(base64String, filename) {
        try {
            // 创建blob对象
            const binaryString = atob(base64String);
            const bytes = new Uint8Array(binaryString.length);

            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            const blob = new Blob([bytes], { type: 'application/pdf' });

            // 创建下载链接
            const downloadLink = document.createElement('a');
            downloadLink.href = URL.createObjectURL(blob);
            downloadLink.download = filename;
            downloadLink.style.display = 'none';

            // 添加到DOM并触发下载
            document.body.appendChild(downloadLink);
            downloadLink.click();

            // 清理
            document.body.removeChild(downloadLink);
            URL.revokeObjectURL(downloadLink.href);

            console.log(`PDF已下载: ${filename}`);

        } catch (error) {
            console.error('下载PDF时出错:', error);
        }
    }

    // 显示通知的函数
    function showNotification(message) {
        // 移除旧通知
        const oldNotification = document.getElementById('pdf-notification');
        if (oldNotification) {
            oldNotification.remove();
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.id = 'pdf-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            max-width: 300px;
            word-wrap: break-word;
        `;
        notification.textContent = message;

        // 添加到页面
        document.body.appendChild(notification);

        // 3秒后移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 显示控制面板
    function showControlPanel() {
        if (!currentPdf) return;

        // 移除旧面板
        const oldPanel = document.getElementById('pdf-control-panel');
        if (oldPanel) {
            oldPanel.remove();
        }

        const panel = document.createElement('div');
        panel.id = 'pdf-control-panel';
        panel.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 13px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            max-width: 300px;
        `;

        const shortTitle = currentPdf.title && currentPdf.title.length > 30
            ? currentPdf.title.substring(0, 30) + '...'
            : (currentPdf.title || 'PDF文档');

        panel.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold; color: #3498db;">
                📄 PDF已拦截
            </div>
            <div style="margin-bottom: 10px; color: #ecf0f1; font-size: 12px;">
                ${shortTitle}
            </div>
            <button id="download-pdf" style="padding: 8px 16px; background: #27ae60; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; width: 100%;">
                下载PDF
            </button>
        `;

        document.body.appendChild(panel);

        // 绑定下载事件
        document.getElementById('download-pdf').onclick = function() {
            if (currentPdf) {
                downloadPdfFromBase64(currentPdf.base64, currentPdf.filename);
                showNotification(`正在下载: ${currentPdf.filename}`);
                // 下载后移除面板
                setTimeout(() => {
                    if (panel.parentNode) {
                        panel.parentNode.removeChild(panel);
                    }
                }, 1000);
            }
        };

        // 5秒后自动变为半透明
        setTimeout(() => {
            if (panel.parentNode) {
                panel.style.opacity = '0.7';
            }
        }, 5000);
    }

    console.log('PDF拦截器与去水印脚本已启动');
})();