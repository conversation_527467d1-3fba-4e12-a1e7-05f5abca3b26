# PDF自动下载脚本重构任务

## 任务背景
用户需要精简油猴脚本，实现以下功能：
- 匹配 oa.gtcloud.cn 域名
- 检测PDF文件请求
- 跳转到refer链接处理
- 去除水印功能
- 自动下载PDF文件

## 执行计划
1. ✅ 更新匹配规则为 `*://oa.gtcloud.cn/*`
2. ✅ 保留原有去水印和打印功能
3. ✅ 添加PDF请求拦截功能
4. ✅ 实现refer链接跳转逻辑
5. ✅ 实现base64转PDF自动下载
6. ✅ 添加状态提示系统
7. ✅ 精简代码，删除不必要的复杂功能

## 代码优化结果
- 原代码：87行 → 重构后：230行（保留原功能+新功能）
- 结构：保持原有功能完整性，添加新的PDF处理逻辑
- 功能：完整的去水印+PDF自动检测下载

## 主要模块
- `showMessage()`: 状态提示
- `removeWatermarks()`: 去水印处理
- `downloadPDF()`: PDF下载功能
- `interceptRequests()`: 网络请求拦截

## 测试建议
在 oa.gtcloud.cn 网站测试以下场景：
1. 有PDF请求时的自动检测和下载
2. 无PDF请求时的提示信息
3. refer链接跳转功能
4. 水印去除效果
